import pandas as pd
import sys
import os

def read_excel_file(file_path):
    """
    Đọc file Excel và hiển thị nội dung
    """
    try:
        # Kiểm tra file có tồn tại không
        if not os.path.exists(file_path):
            print(f"File không tồn tại: {file_path}")
            return
        
        print(f"Đang đọc file: {file_path}")
        print("=" * 50)
        
        # Đọc file Excel
        # Thử đọc tất cả các sheet
        excel_file = pd.ExcelFile(file_path)
        
        print(f"Số lượng sheet: {len(excel_file.sheet_names)}")
        print(f"Tên các sheet: {excel_file.sheet_names}")
        print("=" * 50)
        
        # Đọc và hiển thị nội dung từng sheet
        for sheet_name in excel_file.sheet_names:
            print(f"\n--- SHEET: {sheet_name} ---")
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            print(f"<PERSON><PERSON><PERSON> thước: {df.shape[0]} hàng, {df.shape[1]} cột")
            print(f"Tên các cột: {list(df.columns)}")
            print("\nNội dung:")
            print(df.to_string(index=False))
            print("-" * 30)
            
    except Exception as e:
        print(f"Lỗi khi đọc file Excel: {str(e)}")
        print("Có thể bạn cần cài đặt thư viện: pip install pandas openpyxl")

if __name__ == "__main__":
    file_path = "Backend/AIEvent/tests/Report5_Unit Test.xlsx"
    read_excel_file(file_path)
