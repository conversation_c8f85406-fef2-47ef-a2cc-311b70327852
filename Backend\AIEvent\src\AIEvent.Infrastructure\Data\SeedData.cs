﻿using Microsoft.EntityFrameworkCore;
using AIEvent.Domain.Identity;
using Microsoft.AspNetCore.Identity;
using AIEvent.Domain.Entities;

namespace AIEvent.Infrastructure.Data
{
    public static class SeedData
    {
        private static readonly Guid adminRoleId = Guid.Parse("11111111-1111-1111-1111-111111111111");
        private static readonly Guid userRoleId = Guid.Parse("*************-2222-2222-************");
        private static readonly Guid eventManagerRoleId = Guid.Parse("*************-3333-3333-************");

        private static readonly Guid adminUserId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa");
        private static readonly Guid regularUserId = Guid.Parse("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb");
        private static readonly Guid eventManagerUserId = Guid.Parse("cccccccc-cccc-cccc-cccc-cccccccccccc");
        private static readonly Guid testUserId = Guid.Parse("dddddddd-dddd-dddd-dddd-dddddddddddd");

        public static void Seed(this ModelBuilder modelBuilder)
        {
            SeedRoles(modelBuilder);
            SeedUsers(modelBuilder);
            SeedUserRoles(modelBuilder);
            SeedEventCategory(modelBuilder);
            SeedTag(modelBuilder);
        }

        private static void SeedRoles(ModelBuilder modelBuilder)
        {
            var roles = new[]
            {
                new AppRole
                {
                    Id = adminRoleId,
                    Name = "Admin",
                    NormalizedName = "ADMIN",
                    Description = "Administrator role with full access",
                    CreatedAt = DateTime.UtcNow,
                    ConcurrencyStamp = Guid.NewGuid().ToString()
                },
                new AppRole
                {
                    Id = userRoleId,
                    Name = "User",
                    NormalizedName = "USER",
                    Description = "Regular user role",
                    CreatedAt = DateTime.UtcNow,
                    ConcurrencyStamp = Guid.NewGuid().ToString()
                },
                new AppRole
                {
                    Id = eventManagerRoleId,
                    Name = "EventManager",
                    NormalizedName = "EVENTMANAGER",
                    Description = "Event manager role for managing events",
                    CreatedAt = DateTime.UtcNow,
                    ConcurrencyStamp = Guid.NewGuid().ToString()
                }
            };

            modelBuilder.Entity<AppRole>().HasData(roles);
        }

        private static void SeedUsers(ModelBuilder modelBuilder)
        {
            var passwordHasher = new PasswordHasher<AppUser>();

            var adminUser = new AppUser
            {
                Id = adminUserId,
                UserName = "<EMAIL>",
                NormalizedUserName = "<EMAIL>",
                Email = "<EMAIL>",
                NormalizedEmail = "<EMAIL>", 
                EmailConfirmed = true,
                FullName = "System Administrator",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                SecurityStamp = Guid.NewGuid().ToString(),
                ConcurrencyStamp = Guid.NewGuid().ToString()
            };
            adminUser.PasswordHash = passwordHasher.HashPassword(adminUser, "123");

            var regularUser = new AppUser
            {
                Id = regularUserId,
                UserName = "<EMAIL>",
                NormalizedUserName = "<EMAIL>",
                Email = "<EMAIL>",
                NormalizedEmail = "<EMAIL>", 
                EmailConfirmed = true,
                FullName = "Regular User",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                SecurityStamp = Guid.NewGuid().ToString(),
                ConcurrencyStamp = Guid.NewGuid().ToString()
            };
            regularUser.PasswordHash = passwordHasher.HashPassword(regularUser, "123");

            var eventManagerUser = new AppUser
            {
                Id = eventManagerUserId,
                UserName = "<EMAIL>",
                NormalizedUserName = "<EMAIL>",
                Email = "<EMAIL>",
                NormalizedEmail = "<EMAIL>", 
                EmailConfirmed = true,
                FullName = "Event Manager",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                SecurityStamp = Guid.NewGuid().ToString(),
                ConcurrencyStamp = Guid.NewGuid().ToString()
            };
            eventManagerUser.PasswordHash = passwordHasher.HashPassword(eventManagerUser, "123");

            var testUser = new AppUser
            {
                Id = testUserId,
                UserName = "<EMAIL>",
                NormalizedUserName = "<EMAIL>",
                Email = "<EMAIL>",
                NormalizedEmail = "<EMAIL>",
                EmailConfirmed = true,
                FullName = "Test User",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                SecurityStamp = Guid.NewGuid().ToString(),
                ConcurrencyStamp = Guid.NewGuid().ToString()
            };
            testUser.PasswordHash = passwordHasher.HashPassword(testUser, "123");

            modelBuilder.Entity<AppUser>().HasData(adminUser, regularUser, eventManagerUser, testUser);
        }


        private static void SeedUserRoles(ModelBuilder modelBuilder)
        {
            var userRoles = new[]
            {
                new IdentityUserRole<Guid>
                {
                    UserId = adminUserId,
                    RoleId = adminRoleId
                },
                new IdentityUserRole<Guid>
                {
                    UserId = regularUserId,
                    RoleId = userRoleId
                },
                new IdentityUserRole<Guid>
                {
                    UserId = eventManagerUserId,
                    RoleId = eventManagerRoleId
                },
                new IdentityUserRole<Guid>
                {
                    UserId = testUserId,
                    RoleId = userRoleId
                }
            };

            modelBuilder.Entity<IdentityUserRole<Guid>>().HasData(userRoles);
        }

        private static void SeedEventCategory(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<EventCategory>().HasData(
                new EventCategory
                {
                    Id = Guid.NewGuid(),
                    CategoryName = "Music",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System"
                },
                new EventCategory
                {
                    Id = Guid.NewGuid(),
                    CategoryName = "Technology",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System"
                },
                new EventCategory
                {
                    Id = Guid.NewGuid(),
                    CategoryName = "Sports",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System"
                },
                new EventCategory
                {
                    Id = Guid.NewGuid(),
                    CategoryName = "Education",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System"
                }
            );
        }

        public static void SeedTag(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Tag>().HasData(
                new Tag
                {
                    Id = Guid.NewGuid(),
                    NameTag = "Free",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System"
                },
                new Tag
                {
                    Id = Guid.NewGuid(),
                    NameTag = "Online",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System"
                },
                new Tag
                {
                    Id = Guid.NewGuid(),
                    NameTag = "VIP",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System"
                },
                new Tag
                {
                    Id = Guid.NewGuid(),
                    NameTag = "Workshop",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System"
                }
            );
        }
    }
}
